import type { CSSProperties, Dispatch, FormEvent, SetStateAction } from 'react'
import { useState } from 'react'

interface CreateUserFormProps {
  setUserWasCreated: Dispatch<SetStateAction<boolean>>
}

interface ValidationErrors {
  tooShort: boolean
  tooLong: boolean
  hasSpaces: boolean
  missingNumber: boolean
  missingUppercase: boolean
  missingLowercase: boolean
}

function CreateUserForm({ setUserWasCreated }: CreateUserFormProps) {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({
    tooShort: false,
    tooLong: false,
    hasSpaces: false,
    missingNumber: false,
    missingUppercase: false,
    missingLowercase: false,
  })
  const [apiError, setApiError] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validatePassword = (pwd: string): ValidationErrors => {
    return {
      tooShort: pwd.length < 10,
      tooLong: pwd.length > 24,
      hasSpaces: /\s/.test(pwd),
      missingNumber: !/\d/.test(pwd),
      missingUppercase: !/[A-Z]/.test(pwd),
      missingLowercase: !/[a-z]/.test(pwd),
    }
  }

  const handlePasswordChange = (value: string) => {
    setPassword(value)
    setValidationErrors(validatePassword(value))
    // Clear API error when user starts typing
    if (apiError) {
      setApiError('')
    }
  }

  const handleUsernameChange = (value: string) => {
    setUsername(value)
    // Clear API error when user starts typing
    if (apiError) {
      setApiError('')
    }
  }

  const isPasswordValid = (errors: ValidationErrors): boolean => {
    return !Object.values(errors).some((error) => error)
  }

  const getAuthToken = (): string | null => {
    // Try to get token from URL parameters (common in challenge scenarios)
    const urlParams = new URLSearchParams(window.location.search)
    const tokenFromUrl = urlParams.get('token')
    if (tokenFromUrl) return tokenFromUrl

    // Try to get token from localStorage
    const tokenFromStorage = localStorage.getItem('challenge-token')
    if (tokenFromStorage) return tokenFromStorage

    // Try to get token from sessionStorage
    const tokenFromSession = sessionStorage.getItem('challenge-token')
    if (tokenFromSession) return tokenFromSession

    // Try to extract from current URL path (if it contains the token)
    const pathParts = window.location.pathname.split('/')
    const tokenIndex = pathParts.findIndex((part) => part === 'challenge-details')
    if (tokenIndex !== -1 && tokenIndex + 1 < pathParts.length) {
      return pathParts[tokenIndex + 1]
    }

    return null
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    // Clear previous API errors
    setApiError('')

    // Check if form is valid
    if (!username.trim() || !isPasswordValid(validationErrors)) {
      return
    }

    setIsSubmitting(true)

    try {
      const token = getAuthToken()
      console.log(token)

      if (!token) {
        setApiError('Not authenticated to access this resource.')
        return
      }

      const response = await fetch(
        'https://api.challenge.hennge.com/password-validation-challenge-api/001/challenge-signup',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            username: username.trim(),
            password: password,
          }),
        }
      )

      if (response.ok) {
        // Success - trigger parent component's success state
        setUserWasCreated(true)
        return
      }

      // Handle error responses
      if (response.status === 500) {
        setApiError('Something went wrong, please try again.')
      } else if (response.status === 401 || response.status === 403) {
        setApiError('Not authenticated to access this resource.')
      } else if (response.status === 422) {
        // Handle validation errors from server
        try {
          const errorData = await response.json()
          if (errorData.errors?.includes('not_allowed')) {
            setApiError('Sorry, the entered password is not allowed, please try a different one.')
          } else {
            setApiError('Something went wrong, please try again.')
          }
        } catch {
          setApiError('Something went wrong, please try again.')
        }
      } else {
        setApiError('Something went wrong, please try again.')
      }
    } catch (error) {
      console.error('Submission error:', error)
      setApiError('Something went wrong, please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div style={formWrapper}>
      <form style={form} onSubmit={handleSubmit}>
        <label style={formLabel} htmlFor='username'>
          Username
        </label>
        <input
          id='username'
          style={formInput}
          value={username}
          onChange={(e) => handleUsernameChange(e.target.value)}
          aria-label='Username'
          aria-invalid={!username.trim() ? 'true' : 'false'}
        />

        <label style={formLabel} htmlFor='password'>
          Password
        </label>
        <input
          id='password'
          type='password'
          style={formInput}
          value={password}
          onChange={(e) => handlePasswordChange(e.target.value)}
          aria-label='Password'
          aria-invalid={!isPasswordValid(validationErrors) ? 'true' : 'false'}
        />

        {/* Password validation errors */}
        {password && (
          <div style={validationErrorsContainer}>
            {validationErrors.tooShort && (
              <div style={validationError}>• Password must be at least 10 characters long</div>
            )}
            {validationErrors.tooLong && (
              <div style={validationError}>• Password must be at most 24 characters long</div>
            )}
            {validationErrors.hasSpaces && (
              <div style={validationError}>• Password cannot contain spaces</div>
            )}
            {validationErrors.missingNumber && (
              <div style={validationError}>• Password must contain at least one number</div>
            )}
            {validationErrors.missingUppercase && (
              <div style={validationError}>
                • Password must contain at least one uppercase letter
              </div>
            )}
            {validationErrors.missingLowercase && (
              <div style={validationError}>
                • Password must contain at least one lowercase letter
              </div>
            )}
          </div>
        )}

        {/* API error display */}
        {apiError && (
          <div style={apiErrorContainer}>
            <div style={apiErrorMessage}>{apiError}</div>
          </div>
        )}

        <button
          style={formButton}
          type='submit'
          disabled={isSubmitting || !username.trim() || !isPasswordValid(validationErrors)}
        >
          {isSubmitting ? 'Creating...' : 'Create User'}
        </button>
      </form>
    </div>
  )
}

export { CreateUserForm }

const formWrapper: CSSProperties = {
  maxWidth: '500px',
  width: '80%',
  backgroundColor: '#efeef5',
  padding: '24px',
  borderRadius: '8px',
}

const form: CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
}

const formLabel: CSSProperties = {
  fontWeight: 700,
}

const formInput: CSSProperties = {
  outline: 'none',
  padding: '8px 16px',
  height: '40px',
  fontSize: '14px',
  backgroundColor: '#f8f7fa',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  borderRadius: '4px',
}

const formButton: CSSProperties = {
  outline: 'none',
  borderRadius: '4px',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  backgroundColor: '#7135d2',
  color: 'white',
  fontSize: '16px',
  fontWeight: 500,
  height: '40px',
  padding: '0 8px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: '8px',
  alignSelf: 'flex-end',
  cursor: 'pointer',
}

const validationErrorsContainer: CSSProperties = {
  marginTop: '4px',
  marginBottom: '8px',
}

const validationError: CSSProperties = {
  color: '#d32f2f',
  fontSize: '14px',
  lineHeight: '1.4',
  marginBottom: '2px',
}

const apiErrorContainer: CSSProperties = {
  marginTop: '8px',
  marginBottom: '8px',
}

const apiErrorMessage: CSSProperties = {
  color: '#d32f2f',
  fontSize: '14px',
  lineHeight: '1.4',
  padding: '8px',
  backgroundColor: '#ffebee',
  border: '1px solid #ffcdd2',
  borderRadius: '4px',
}
