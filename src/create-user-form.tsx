import type { CSSProperties, Dispatch, FormEvent, SetStateAction } from 'react'
import { useState } from 'react'

interface CreateUserFormProps {
  setUserWasCreated: Dispatch<SetStateAction<boolean>>
}

interface ValidationErrors {
  tooShort: boolean
  tooLong: boolean
  hasSpaces: boolean
  missingNumber: boolean
  missingUppercase: boolean
  missingLowercase: boolean
}

function CreateUserForm({ setUserWasCreated }: CreateUserFormProps) {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({
    tooShort: false,
    tooLong: false,
    hasSpaces: false,
    missingNumber: false,
    missingUppercase: false,
    missingLowercase: false,
  })
  const [apiError, setApiError] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validatePassword = (pwd: string): ValidationErrors => {
    return {
      tooShort: pwd.length < 10,
      tooLong: pwd.length > 24,
      hasSpaces: /\s/.test(pwd),
      missingNumber: !/\d/.test(pwd),
      missingUppercase: !/[A-Z]/.test(pwd),
      missingLowercase: !/[a-z]/.test(pwd),
    }
  }

  const handlePasswordChange = (value: string) => {
    setPassword(value)
    setValidationErrors(validatePassword(value))
    // Clear API error when user starts typing
    if (apiError) {
      setApiError('')
    }
  }

  const handleUsernameChange = (value: string) => {
    setUsername(value)
    // Clear API error when user starts typing
    if (apiError) {
      setApiError('')
    }
  }

  const isPasswordValid = (errors: ValidationErrors): boolean => {
    return !Object.values(errors).some((error) => error)
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()

    // Clear previous API errors
    setApiError('')

    // Check if form is valid
    if (!username.trim() || !isPasswordValid(validationErrors)) {
      return
    }

    setIsSubmitting(true)

    try {
      const token =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************.No8aR3jPD5XVjqvp-SwF00HeA9GRwGB0F9_k2r1Z8Y0'
      console.log('Token found:', token)

      if (!token) {
        console.log('No token found, showing auth error')
        setApiError('Not authenticated to access this resource.')
        return
      }

      console.log('Making API request with:', {
        username: username.trim(),
        password: '***hidden***',
        token: token ? 'present' : 'missing',
      })

      const response = await fetch(
        'https://api.challenge.hennge.com/password-validation-challenge-api/001/challenge-signup',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            username: username.trim(),
            password: password,
          }),
        }
      )

      console.log('API Response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      })

      if (response.ok) {
        console.log('Success! Setting user as created')
        // Success - trigger parent component's success state
        setUserWasCreated(true)
        return
      }

      // Handle error responses
      console.log('Handling error response, status:', response.status)

      if (response.status === 500) {
        setApiError('Something went wrong, please try again.')
      } else if (response.status === 401 || response.status === 403) {
        setApiError('Not authenticated to access this resource.')
      } else if (response.status === 422) {
        // Handle validation errors from server
        try {
          const errorData = await response.json()
          console.log('422 Error data:', errorData)
          if (errorData.errors?.includes('not_allowed')) {
            setApiError('Sorry, the entered password is not allowed, please try a different one.')
          } else {
            setApiError('Something went wrong, please try again.')
          }
        } catch (parseError) {
          console.log('Error parsing 422 response:', parseError)
          setApiError('Something went wrong, please try again.')
        }
      } else {
        // Try to get response text for debugging
        try {
          const responseText = await response.text()
          console.log('Unexpected response:', response.status, responseText)
        } catch (e) {
          console.log('Could not read response text')
        }
        setApiError('Something went wrong, please try again.')
      }
    } catch (error) {
      console.error('Submission error:', error)
      setApiError('Something went wrong, please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div style={formWrapper}>
      <form style={form} onSubmit={handleSubmit}>
        <label style={formLabel} htmlFor='username'>
          Username
        </label>
        <input
          id='username'
          style={formInput}
          value={username}
          onChange={(e) => handleUsernameChange(e.target.value)}
          aria-label='Username'
          aria-invalid={!username.trim() ? 'true' : 'false'}
        />

        <label style={formLabel} htmlFor='password'>
          Password
        </label>
        <input
          id='password'
          type='password'
          style={formInput}
          value={password}
          onChange={(e) => handlePasswordChange(e.target.value)}
          aria-label='Password'
          aria-invalid={!isPasswordValid(validationErrors) ? 'true' : 'false'}
        />

        {/* Password validation errors */}
        {password && (
          <div style={validationErrorsContainer}>
            {validationErrors.tooShort && (
              <div style={validationError}>• Password must be at least 10 characters long</div>
            )}
            {validationErrors.tooLong && (
              <div style={validationError}>• Password must be at most 24 characters long</div>
            )}
            {validationErrors.hasSpaces && (
              <div style={validationError}>• Password cannot contain spaces</div>
            )}
            {validationErrors.missingNumber && (
              <div style={validationError}>• Password must contain at least one number</div>
            )}
            {validationErrors.missingUppercase && (
              <div style={validationError}>
                • Password must contain at least one uppercase letter
              </div>
            )}
            {validationErrors.missingLowercase && (
              <div style={validationError}>
                • Password must contain at least one lowercase letter
              </div>
            )}
          </div>
        )}

        {/* API error display */}
        {apiError && (
          <div style={apiErrorContainer}>
            <div style={apiErrorMessage}>{apiError}</div>
          </div>
        )}

        <button
          style={formButton}
          type='submit'
          disabled={isSubmitting || !username.trim() || !isPasswordValid(validationErrors)}
        >
          {isSubmitting ? 'Creating...' : 'Create User'}
        </button>
      </form>
    </div>
  )
}

export { CreateUserForm }

const formWrapper: CSSProperties = {
  maxWidth: '500px',
  width: '80%',
  backgroundColor: '#efeef5',
  padding: '24px',
  borderRadius: '8px',
}

const form: CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  gap: '8px',
}

const formLabel: CSSProperties = {
  fontWeight: 700,
}

const formInput: CSSProperties = {
  outline: 'none',
  padding: '8px 16px',
  height: '40px',
  fontSize: '14px',
  backgroundColor: '#f8f7fa',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  borderRadius: '4px',
}

const formButton: CSSProperties = {
  outline: 'none',
  borderRadius: '4px',
  border: '1px solid rgba(0, 0, 0, 0.12)',
  backgroundColor: '#7135d2',
  color: 'white',
  fontSize: '16px',
  fontWeight: 500,
  height: '40px',
  padding: '0 8px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: '8px',
  alignSelf: 'flex-end',
  cursor: 'pointer',
}

const validationErrorsContainer: CSSProperties = {
  marginTop: '4px',
  marginBottom: '8px',
}

const validationError: CSSProperties = {
  color: '#d32f2f',
  fontSize: '14px',
  lineHeight: '1.4',
  marginBottom: '2px',
}

const apiErrorContainer: CSSProperties = {
  marginTop: '8px',
  marginBottom: '8px',
}

const apiErrorMessage: CSSProperties = {
  color: '#d32f2f',
  fontSize: '14px',
  lineHeight: '1.4',
  padding: '8px',
  backgroundColor: '#ffebee',
  border: '1px solid #ffcdd2',
  borderRadius: '4px',
}
