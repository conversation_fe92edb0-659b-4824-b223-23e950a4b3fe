{"version": 3, "names": ["_toSetter", "fn", "args", "thisArg", "l", "length", "Object", "defineProperty", "set", "v", "apply"], "sources": ["../../src/helpers/toSetter.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n\nexport default function _toSetter(fn: Function, args: any[], thisArg: any) {\n  if (!args) args = [];\n  var l = args.length++;\n  return Object.defineProperty({}, \"_\", {\n    set: function (v) {\n      args[l] = v;\n      fn.apply(thisArg, args);\n    },\n  });\n}\n"], "mappings": ";;;;;;AAEe,SAASA,SAASA,CAACC,EAAY,EAAEC,IAAW,EAAEC,OAAY,EAAE;EACzE,IAAI,CAACD,IAAI,EAAEA,IAAI,GAAG,EAAE;EACpB,IAAIE,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAE;EACrB,OAAOC,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;IACpCC,GAAG,EAAE,SAAAA,CAAUC,CAAC,EAAE;MAChBP,IAAI,CAACE,CAAC,CAAC,GAAGK,CAAC;MACXR,EAAE,CAACS,KAAK,CAACP,OAAO,EAAED,IAAI,CAAC;IACzB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}